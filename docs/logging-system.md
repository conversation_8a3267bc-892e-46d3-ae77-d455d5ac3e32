# Electron主进程日志文件持久化系统

## 概述

本系统实现了Electron主进程的日志文件持久化功能，解决了打包后无法查看主进程控制台日志的问题。前端渲染进程的日志可以通过开发者工具查看，无需额外的文件记录。

## 核心特性

### 1. 日志文件持久化
- 日志文件保存在应用数据目录下的 `logs` 文件夹中
- 支持开发环境和生产环境
- 自动创建日志目录

### 2. 日志轮转
- 单个日志文件最大 10MB
- 保留最近 10 个日志文件
- 自动清理过期日志

### 3. 日志级别
- `error`: 错误信息
- `warn`: 警告信息
- `info`: 一般信息
- `debug`: 调试信息
- 开发环境默认 `debug` 级别，生产环境默认 `info` 级别

### 4. 模块化日志
- 支持按模块分类记录日志
- 便于问题定位和调试

### 5. 格式化输出
- 包含时间戳、日志级别、进程信息
- 支持结构化数据记录

## 使用方法

### 主进程中使用

```typescript
import { logger, createLogger } from './logger/index';

// 基本日志记录
logger.info('应用启动');
logger.warn('配置文件缺失');
logger.error('数据库连接失败');
logger.debug('调试信息');

// 创建模块专用日志记录器
const uploadLogger = createLogger('upload');
uploadLogger.info('开始上传文件');
uploadLogger.error('上传失败', { fileName: 'test.txt', error: 'Network error' });
```

## 配置选项

### LoggerConfig 接口

```typescript
interface LoggerConfig {
  logPath?: string;        // 日志文件存储路径
  level?: LogLevel;        // 日志级别
  maxSize?: number;        // 日志文件大小限制（字节）
  maxFiles?: number;       // 保留的日志文件数量
  console?: boolean;       // 是否在控制台输出
  timestamp?: boolean;     // 是否包含时间戳
  processInfo?: boolean;   // 是否包含进程信息
  rotation?: boolean;      // 是否启用日志轮转
  moduleInfo?: boolean;    // 是否包含模块信息
}
```

### 默认配置

```typescript
const DEFAULT_CONFIG: LoggerConfig = {
  level: 'info',
  maxSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 5,
  console: true,
  timestamp: true,
  processInfo: true,
  rotation: true,
  moduleInfo: true
};
```

## 日志格式

### 标准格式
```
[2024-01-15T10:30:45.123Z] [INFO] [main] [UPLOAD] 开始上传: document.pdf (1.2 MB)
[2024-01-15T10:30:46.456Z] [DEBUG] [renderer] [UPLOAD] 上传进度: document.pdf - 25%
[2024-01-15T10:30:50.789Z] [INFO] [main] [UPLOAD] 上传完成: document.pdf
```

### 格式说明
- `[时间戳]`: ISO 8601 格式的时间戳
- `[级别]`: 日志级别（INFO、WARN、ERROR、DEBUG）
- `[进程]`: 进程类型（main、renderer）
- `[模块]`: 模块名称（可选）
- `消息内容`: 实际的日志消息

## 文件结构

```
应用数据目录/
└── logs/
    ├── main.log          # 当前主日志文件
    ├── main.log.1        # 轮转的日志文件
    ├── main.log.2
    └── ...
```

### 生产环境验证

1. 打包应用
2. 运行打包后的应用
3. 执行一些操作
4. 检查应用数据目录下的 `logs` 文件夹
5. 验证日志文件是否正确生成

## 日志管理界面

系统提供了一个日志管理界面组件 `LogViewer.vue`，包含以下功能：

- 显示日志文件路径
- 打开日志文件夹
- 清除日志文件
- 测试日志记录
- 使用说明

### 使用日志管理界面

```vue
<template>
  <LogViewer />
</template>

<script setup>
import LogViewer from '@/components/LogViewer/LogViewer.vue'
</script>
```

## 最佳实践

### 1. 日志级别使用建议
- `error`: 系统错误、异常情况
- `warn`: 潜在问题、性能警告
- `info`: 重要操作、状态变化
- `debug`: 详细调试信息

### 2. 模块化日志
- 为不同功能模块创建专用日志记录器
- 使用有意义的模块名称
- 保持模块名称一致性

### 3. 结构化数据
- 使用对象传递复杂数据
- 避免在日志消息中包含敏感信息
- 保持日志消息简洁明了

### 4. 性能考虑
- 避免在高频操作中记录过多日志
- 在生产环境中适当调整日志级别
- 定期清理过期日志文件

## 故障排查

### 常见问题

1. **日志文件未生成**
   - 检查应用数据目录权限
   - 确认日志系统已正确初始化
   - 查看控制台错误信息

2. **日志文件过大**
   - 检查日志轮转配置
   - 调整 `maxSize` 和 `maxFiles` 参数
   - 考虑提高日志级别

3. **性能影响**
   - 减少调试日志输出
   - 调整日志缓冲设置
   - 考虑异步日志记录

### 调试命令

```javascript
// 在开发者控制台中执行
await window.electronAPI.logger.getLogPath()
await window.electronAPI.logger.clearLogs()
```

## 迁移指南

### 从 console.log 迁移

```typescript
// 旧方式
console.log('用户登录成功');
console.error('登录失败:', error);

// 新方式
logger.info('用户登录成功', { module: 'auth', action: 'login' });
logger.error('登录失败', { module: 'auth', action: 'login', data: error });
```

### 从现有调试系统迁移

现有的 `src/lib/debug.ts` 已经集成了新的日志系统，可以逐步迁移到新的API。
