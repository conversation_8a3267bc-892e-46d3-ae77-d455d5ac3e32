import { BrowserWindow } from "electron";
import { SevenZipExtractionManager } from "./extractionManager";
import { registerExtractionIpcHandlers } from "./ipcHandlers";
import type { ExtractionConfig } from "./types";

// 导出类型
export type {
  ExtractionTask,
  ExtractionConfig,
  ExtractionStatus,
  ExtractionEvents,
  ExtractionApiResponse,
  ExtractionStoreData,
  ExtractionProgress,
  ExtractionResult,
  SevenZipOutput,
  FileConflictAction,
} from "./types";

// 导出 API 和监听器接口
export type { ExtractionPreloadApi, ExtractionEventListeners, SimplifiedExtractionPreloadApi } from "./preloadApi";

// 导出 API 创建函数
export { createExtractionApi, createExtractionEventListeners, createSimplifiedExtractionPreloadApi } from "./preloadApi";

// 导出解压缩管理器
export { SevenZipExtractionManager } from "./extractionManager";

/**
 * 初始化解压缩模块
 */
export function initializeExtractionModule(
  mainWindow: BrowserWindow,
  config: ExtractionConfig
): {
  extractionManager: SevenZipExtractionManager;
  cleanup: () => void;
} {
  // 动态导入 logger，避免在 preload 脚本中包含
  const { createLogger } = require("../logger/index");
  const extractorLogger = createLogger("extractor");

  extractorLogger.info("🚀 初始化 7z 解压缩模块");

  // 创建解压缩管理器
  const extractionManager = new SevenZipExtractionManager(config);

  // 注册 IPC 处理器
  registerExtractionIpcHandlers(extractionManager);

  // 转发事件到渲染进程
  const forwardEventToRenderer = (eventName: string) => {
    return (...args: any[]) => {
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send(eventName, ...args);
      }
    };
  };

  // 绑定事件转发
  extractionManager.on("task-created", forwardEventToRenderer("extraction-task-created"));
  extractionManager.on("task-progress", forwardEventToRenderer("extraction-task-progress"));
  extractionManager.on("task-status-changed", forwardEventToRenderer("extraction-task-status-changed"));
  extractionManager.on("task-completed", forwardEventToRenderer("extraction-task-completed"));
  extractionManager.on("task-error", forwardEventToRenderer("extraction-task-error"));
  extractionManager.on("password-required", forwardEventToRenderer("extraction-password-required"));
  extractionManager.on("file-conflict", forwardEventToRenderer("extraction-file-conflict"));

  // 清理函数
  const cleanup = () => {
    extractorLogger.info("🧹 清理解压缩模块资源");
    extractionManager.removeAllListeners();
  };

  extractorLogger.info("✅ 7z 解压缩模块初始化完成");

  return {
    extractionManager,
    cleanup,
  };
}
